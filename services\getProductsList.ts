import useSWR from 'swr'
import { useSearchParams } from 'next/navigation'

const fetcher = (url: string) => fetch(url).then(res => res.json())

export function getProductsList() {
  const searchParams = useSearchParams()
  
  // Build query string from search params
  const queryString = searchParams.toString()
  const url = `/api/productsList${queryString ? `?${queryString}` : ''}`
  
  const { data, error, isLoading, mutate } = useSWR(url, fetcher)
  
  return {
    products: data || [],
    isLoading,
    isError: error,
    refetch: mutate
  }
}