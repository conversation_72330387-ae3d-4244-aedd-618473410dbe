"use client";

import * as React from "react";
import { EditIcon, TrashIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Product } from "@/types/Product";

interface ProductMobileListItemProps {
  product: Product;
  onEdit?: (product: Product) => void;
  onDelete?: (productId: string) => void;
  className?: string;
}

export function ProductMobileListItem({
  product,
  onEdit,
  onDelete,
  className,
}: ProductMobileListItemProps) {
  const handleEdit = () => {
    console.log("Edit product:", product);
    onEdit?.(product);
  };

  const handleDelete = () => {
    console.log("Delete product:", product.product_id);
    onDelete?.(product.product_id);
  };

  return (
    <div
      className={`relative rounded-lg border bg-card p-4 shadow-sm ${
        className || ""
      }`}
    >
      {/* Header with product name and action buttons */}
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-foreground pr-2">
            {product.name}
          </h3>
          {product.is_shared && (
            <span className="text-base text-muted-foreground">
              Produkt udostępniony
            </span>
          )}
        </div>

        <div className="flex gap-1 shrink-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleEdit}
            className="h-8 w-8"
            disabled={product.is_shared}
          >
            <EditIcon className="h-4 w-4" />
            <span className="sr-only">Edit {product.name}</span>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDelete}
            className="h-8 w-8 text-destructive hover:text-destructive"
            disabled={product.is_shared}
          >
            <TrashIcon className="h-4 w-4" />
            <span className="sr-only">Delete {product.name}</span>
          </Button>
        </div>
      </div>

      {/* Nutritional information grid */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Calories</p>
          <p className="text-base font-medium">{product.calories} kcal/100g</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Protein</p>
          <p className="text-base font-medium">{product.protein}g</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Carbs</p>
          <p className="text-base font-medium">{product.carbs}g</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Fat</p>
          <p className="text-base font-medium">{product.fat}g</p>
        </div>
      </div>
    </div>
  );
}

export type { Product, ProductMobileListItemProps };
