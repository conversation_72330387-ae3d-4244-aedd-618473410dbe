"use client";
import PageHeader from "@/components/general/PageHeader";
import { PaginationComponent } from "@/components/general/PaginationComponent";
import { ProductDetailDefaultMeasurementCard } from "@/components/product/productDetail/ProductDetailDefaultMeasurmentCard";
import { ProductDetailsDefaultMeasurementChart } from "@/components/product/productDetail/ProductDetailsDefauldMeasurmentChart";
import { ProductDetailsMeasurmentMobileList } from "@/components/product/productDetail/ProductDetailsMeasurmentMobileList";
import { ProductDetailsMeasurementTable } from "@/components/product/productDetail/ProductDetailsMeasurmentTable";
import { Button } from "@/components/ui/button";
import PageWrapper from "@/components/wrappers/authPageWrapper";
import { SAMPLE_MEASUREMENTS } from "@/data/product/product";
import { useIsMobile } from "@/hooks/use-mobile";
import { Edit, TrashIcon } from "lucide-react";
import React, { use } from "react";

interface ProductDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  const isMobile = useIsMobile();
  const { id } = use(params);
  console.log(id);
  return (
    <PageWrapper>
      <PageHeader
        title="Szczegóły produktu kurczak"
        actions={
          <>
            <Button size={"icon"} variant={"outline"}>
              <Edit className="w-4 h-4" />
            </Button>
            <Button
              size={"icon"}
              variant={"outline"}
              className="text-destructive hover:text-destructive"
            >
              <TrashIcon className="w-4 h-4" />
            </Button>
          </>
        }
      />
      <div className="overflow-auto flex flex-col gap-4 p-4 flex-1">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:items-start">
          <ProductDetailDefaultMeasurementCard
            calories={100}
            protein={10}
            carbs={10}
            fat={10}
            unit={"g"}
          />
          <ProductDetailsDefaultMeasurementChart
            calories={100}
            protein={10}
            carbs={10}
            fat={10}
          />
        </div>
        {isMobile ? (
          <ProductDetailsMeasurmentMobileList
            measurements={SAMPLE_MEASUREMENTS}
          />
        ) : (
          <ProductDetailsMeasurementTable measurements={SAMPLE_MEASUREMENTS} />
        )}
        <PaginationComponent pageSize={10} totalItems={100} />
      </div>
    </PageWrapper>
  );
}
