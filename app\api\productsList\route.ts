import { createClient } from "@/utlis/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    let {
      data: products,
      error,
      count,
    } = await supabase
      .from("products")
      .select("product_id, name, is_shared , measurements(kcal, protein, carbs, fat)", { count: "exact" })
      .eq("measurements.is_default", true)
      .range(0, 5);
    console.log(products);
    console.log(error);
    console.log(count);
    if (error) {
      throw error;
    }
    return NextResponse.json(products);
  } catch (err) {
    console.log(err);
  }
}
