"use client";
import PageWrapper from "@/components/wrappers/authPageWrapper";
import PageHeader from "@/components/general/PageHeader";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ProductFilter } from "@/components/product/ProductFilter";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { SAMPLE_PRODUCTS } from "@/data/product/product";
import { ProductTable } from "@/components/product/ProductTable";
import { PaginationComponent } from "@/components/general/PaginationComponent";
import { ProductMobileList } from "@/components/product/ProductMobileList";
import { useIsMobile } from "@/hooks/use-mobile";
import { ProductAddEditPopup } from "@/components/product/ProductAddEditPopup";
import { ProductFormData } from "@/types/Measurement";

export default function ProductPage() {
  const useSearchParam = useSearchParams();
  const isMobile = useIsMobile();
  const [isAddPopupOpen, setIsAddPopupOpen] = useState(false);

  useEffect(() => {
    console.log(useSearchParam.get("sort"));
    console.log(useSearchParam.get("searchQuery"));
    console.log(useSearchParam.get("myProducts"));
  }, [useSearchParam]);

  const handleAddProduct = (data: ProductFormData) => {
    console.log("Adding product:", data);
    // TODO: Implement actual product creation logic
    // This would typically involve calling an API to save the product
  };

  return (
    <PageWrapper>
      <PageHeader
        title="Produkty"
        actions={
          <Button onClick={() => setIsAddPopupOpen(true)}>
            <Plus className="w-4 h-4" />
            Dodaj produkt
          </Button>
        }
      />
      <ProductFilter />
      {isMobile ? (
        <ProductMobileList products={SAMPLE_PRODUCTS} />
      ) : (
        <ProductTable products={SAMPLE_PRODUCTS} />
      )}
      <PaginationComponent pageSize={10} totalItems={100} />

      <ProductAddEditPopup
        open={isAddPopupOpen}
        onOpenChange={setIsAddPopupOpen}
        onSubmit={handleAddProduct}
        mode="add"
      />
    </PageWrapper>
  );
}
